{"name": "@roamhq/wrtc", "description": "Standards-compliant WebRTC implementation for Node", "keywords": ["webrtc", "p2p", "peer"], "version": "0.8.0", "author": "<PERSON> <<EMAIL>> (http://blog.modeswitch.org)", "homepage": "https://github.com/WonderInventions/node-webrtc", "bugs": "https://github.com/WonderInventions/node-webrtc/issues", "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "http://github.com/WonderInventions/node-webrtc.git"}, "main": "lib/index.js", "types": "types/index.d.ts", "browser": "lib/browser.js", "files": ["AUTHORS", "CHANGELOG.md", "lib", "types"], "optionalDependencies": {"@roamhq/wrtc-darwin-arm64": "0.8.0", "@roamhq/wrtc-darwin-x64": "0.8.0", "@roamhq/wrtc-linux-arm64": "0.8.1", "@roamhq/wrtc-linux-x64": "0.8.1", "@roamhq/wrtc-win32-x64": "0.8.0", "domexception": "^4.0.0"}, "devDependencies": {"@fidm/x509": "^1.2.1", "cmake-js": "^7.3.0", "cross-env": "^5.1.4", "eslint": "^4.14.0", "node-addon-api": "^7.0.0", "patch-package": "^8.0.0", "recursive-copy": "^2.0.14", "simple-peer": "~9.7.0", "tape": "^5.6.1", "temp": "^0.9.4"}, "scripts": {"patch": "patch-package --error-on-warn", "build": "node scripts/build-from-source.js", "make-prebuilt": "node scripts/make-prebuilt.js", "install-example": "node scripts/install-example.js", "lint": "eslint lib/*.js lib/**/*.js test/*.js test/**/*.js scripts/*.js", "test": "node --expose-gc test/all.js"}}