"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Ref = Ref;
const index_1 = require("../symbols/index");
// ------------------------------------------------------------------
// ValueGuard
// ------------------------------------------------------------------
const value_1 = require("../guard/value");
/** `[<PERSON><PERSON>]` Creates a Ref type. */
function Ref(unresolved, options = {}) {
    if ((0, value_1.IsString)(unresolved))
        return { ...options, [index_1.Kind]: 'Ref', $ref: unresolved };
    if ((0, value_1.IsUndefined)(unresolved.$id))
        throw new Error('Reference target type must specify an $id');
    return {
        ...options,
        [index_1.Kind]: 'Ref',
        $ref: unresolved.$id,
    };
}
