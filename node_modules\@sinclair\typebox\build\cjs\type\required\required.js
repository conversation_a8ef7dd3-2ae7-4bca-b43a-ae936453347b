"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Required = Required;
const index_1 = require("../intersect/index");
const index_2 = require("../union/index");
const index_3 = require("../object/index");
const index_4 = require("../symbols/index");
const type_1 = require("../clone/type");
const index_5 = require("../discard/index");
const required_from_mapped_result_1 = require("./required-from-mapped-result");
// ------------------------------------------------------------------
// TypeGuard
// ------------------------------------------------------------------
const kind_1 = require("../guard/kind");
// prettier-ignore
function FromRest(T) {
    return T.map(L => RequiredResolve(L));
}
// prettier-ignore
function FromProperties(T) {
    const Acc = {};
    for (const K of globalThis.Object.getOwnPropertyNames(T))
        Acc[K] = (0, index_5.Discard)(T[K], [index_4.OptionalKind]);
    return Acc;
}
// ------------------------------------------------------------------
// RequiredResolve
// ------------------------------------------------------------------
// prettier-ignore
function RequiredResolve(T) {
    return ((0, kind_1.IsIntersect)(T) ? (0, index_1.Intersect)(FromRest(T.allOf)) :
        (0, kind_1.IsUnion)(T) ? (0, index_2.Union)(FromRest(T.anyOf)) :
            (0, kind_1.IsObject)(T) ? (0, index_3.Object)(FromProperties(T.properties)) :
                (0, index_3.Object)({}));
}
/** `[Json]` Constructs a type where all properties are required */
function Required(T, options = {}) {
    if ((0, kind_1.IsMappedResult)(T)) {
        return (0, required_from_mapped_result_1.RequiredFromMappedResult)(T, options);
    }
    else {
        const D = (0, index_5.Discard)(T, [index_4.TransformKind, '$id', 'required']);
        const R = (0, type_1.CloneType)(RequiredResolve(T), options);
        return { ...D, ...R };
    }
}
