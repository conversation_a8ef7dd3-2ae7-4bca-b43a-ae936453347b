{"version": 3, "file": "blake1.d.ts", "sourceRoot": "", "sources": ["src/blake1.ts"], "names": [], "mappings": "AA6BA,OAAO,EAAE,IAAI,EAAE,KAAK,KAAK,EAAgD,MAAM,YAAY,CAAC;AAE5F,4CAA4C;AAC5C,MAAM,MAAM,SAAS,GAAG;IACtB,IAAI,CAAC,EAAE,KAAK,CAAC;CACd,CAAC;AAKF,uBAAe,MAAM,CAAC,CAAC,SAAS,MAAM,CAAC,CAAC,CAAC,CAAE,SAAQ,IAAI,CAAC,CAAC,CAAC;IAatD,QAAQ,CAAC,QAAQ,EAAE,MAAM;IACzB,QAAQ,CAAC,SAAS,EAAE,MAAM;IAC1B,OAAO,CAAC,UAAU;IAClB,OAAO,CAAC,UAAU;IAElB,SAAS,CAAC,SAAS,EAAE,WAAW;IAjBlC,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,MAAM,SAAK;IACrB,SAAS,CAAC,GAAG,SAAK;IAClB,SAAS,CAAC,SAAS,UAAS;IAE5B,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;IAC7B,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC;IACzB,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC;IAC5B,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI;IAC7E,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,MAAM,EAAE;IAClC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI;gBAEpC,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EAClB,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,MAAM,EAC1B,OAAO,EAAE,MAAM,EACL,SAAS,EAAE,WAAW,EAChC,IAAI,GAAE,SAAc;IAmBtB,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IA6BzB,OAAO,IAAI,IAAI;IAOf,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;IAarB,UAAU,CAAC,GAAG,EAAE,UAAU,GAAG,IAAI;IA4BjC,MAAM,IAAI,UAAU;CAOrB;AA6BD,cAAM,SAAU,SAAQ,MAAM,CAAC,SAAS,CAAC;IACvC,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,EAAE,CAAS;gBACP,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,GAAE,SAAc;IAWxF,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAKjF,SAAS,CAAC,GAAG,CACX,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAC7F,IAAI;IAUP,OAAO,IAAI,IAAI;IAIf,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,UAAO,GAAG,IAAI;CAiDlE;AA0FD,cAAM,SAAU,SAAQ,MAAM,CAAC,SAAS,CAAC;IAkB9B,SAAS,EAAE,MAAM;IAjB1B,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,GAAG,CAAS;gBAEX,SAAS,EAAE,MAAM,EACxB,EAAE,EAAE,WAAW,EACf,UAAU,EAAE,MAAM,EAClB,IAAI,GAAE,SAAc;IAqBtB,SAAS,CAAC,GAAG,IAAI;QACf,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAC9D,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;QAAE,MAAM;KAC/D;IAKD,SAAS,CAAC,GAAG,CACX,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAClD,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GACjD,IAAI;IAkBP,OAAO,IAAI,IAAI;IAIf,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,UAAO,GAAG,IAAI;CAkDlE;AAED,qBAAa,QAAS,SAAQ,SAAS;gBACzB,IAAI,GAAE,SAAc;CAGjC;AACD,qBAAa,QAAS,SAAQ,SAAS;gBACzB,IAAI,GAAE,SAAc;CAIjC;AACD,qBAAa,QAAS,SAAQ,SAAS;gBACzB,IAAI,GAAE,SAAc;CAGjC;AACD,qBAAa,QAAS,SAAQ,SAAS;gBACzB,IAAI,GAAE,SAAc;CAGjC;AACD,+BAA+B;AAC/B,eAAO,MAAM,QAAQ,EAAE;IACrB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;IACvD,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;CACoD,CAAC;AAC/F,+BAA+B;AAC/B,eAAO,MAAM,QAAQ,EAAE;IACrB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;IACvD,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;CACoD,CAAC;AAC/F,+BAA+B;AAC/B,eAAO,MAAM,QAAQ,EAAE;IACrB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;IACvD,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;CACoD,CAAC;AAC/F,+BAA+B;AAC/B,eAAO,MAAM,QAAQ,EAAE;IACrB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;IACvD,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;CACoD,CAAC"}