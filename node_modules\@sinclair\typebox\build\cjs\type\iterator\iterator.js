"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Iterator = Iterator;
const type_1 = require("../clone/type");
const index_1 = require("../symbols/index");
/** `[JavaScript]` Creates an Iterator type */
function Iterator(items, options = {}) {
    return {
        ...options,
        [index_1.Kind]: 'Iterator',
        type: 'Iterator',
        items: (0, type_1.CloneType)(items),
    };
}
