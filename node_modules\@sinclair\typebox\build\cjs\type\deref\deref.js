"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Deref = Deref;
const type_1 = require("../clone/type");
const index_1 = require("../discard/index");
const value_1 = require("../guard/value");
// ------------------------------------------------------------------
// TypeGuard
// ------------------------------------------------------------------
const kind_1 = require("../guard/kind");
function FromRest(schema, references) {
    return schema.map((schema) => Deref(schema, references));
}
// prettier-ignore
function FromProperties(properties, references) {
    const Acc = {};
    for (const K of globalThis.Object.getOwnPropertyNames(properties)) {
        Acc[K] = Deref(properties[K], references);
    }
    return Acc;
}
// prettier-ignore
function FromConstructor(schema, references) {
    schema.parameters = FromRest(schema.parameters, references);
    schema.returns = Deref(schema.returns, references);
    return schema;
}
// prettier-ignore
function FromFunction(schema, references) {
    schema.parameters = FromRest(schema.parameters, references);
    schema.returns = Deref(schema.returns, references);
    return schema;
}
// prettier-ignore
function FromIntersect(schema, references) {
    schema.allOf = FromRest(schema.allOf, references);
    return schema;
}
// prettier-ignore
function FromUnion(schema, references) {
    schema.anyOf = FromRest(schema.anyOf, references);
    return schema;
}
// prettier-ignore
function FromTuple(schema, references) {
    if ((0, value_1.IsUndefined)(schema.items))
        return schema;
    schema.items = FromRest(schema.items, references);
    return schema;
}
// prettier-ignore
function FromArray(schema, references) {
    schema.items = Deref(schema.items, references);
    return schema;
}
// prettier-ignore
function FromObject(schema, references) {
    schema.properties = FromProperties(schema.properties, references);
    return schema;
}
// prettier-ignore
function FromPromise(schema, references) {
    schema.item = Deref(schema.item, references);
    return schema;
}
// prettier-ignore
function FromAsyncIterator(schema, references) {
    schema.items = Deref(schema.items, references);
    return schema;
}
// prettier-ignore
function FromIterator(schema, references) {
    schema.items = Deref(schema.items, references);
    return schema;
}
// prettier-ignore
function FromRef(schema, references) {
    const target = references.find(remote => remote.$id === schema.$ref);
    if (target === undefined)
        throw Error(`Unable to dereference schema with $id ${schema.$ref}`);
    const discard = (0, index_1.Discard)(target, ['$id']);
    return Deref(discard, references);
}
// prettier-ignore
function DerefResolve(schema, references) {
    return ((0, kind_1.IsConstructor)(schema) ? FromConstructor(schema, references) :
        (0, kind_1.IsFunction)(schema) ? FromFunction(schema, references) :
            (0, kind_1.IsIntersect)(schema) ? FromIntersect(schema, references) :
                (0, kind_1.IsUnion)(schema) ? FromUnion(schema, references) :
                    (0, kind_1.IsTuple)(schema) ? FromTuple(schema, references) :
                        (0, kind_1.IsArray)(schema) ? FromArray(schema, references) :
                            (0, kind_1.IsObject)(schema) ? FromObject(schema, references) :
                                (0, kind_1.IsPromise)(schema) ? FromPromise(schema, references) :
                                    (0, kind_1.IsAsyncIterator)(schema) ? FromAsyncIterator(schema, references) :
                                        (0, kind_1.IsIterator)(schema) ? FromIterator(schema, references) :
                                            (0, kind_1.IsRef)(schema) ? FromRef(schema, references) :
                                                schema);
}
// ------------------------------------------------------------------
// TDeref
// ------------------------------------------------------------------
/** `[Json]` Creates a dereferenced type */
function Deref(schema, references) {
    return DerefResolve((0, type_1.CloneType)(schema), (0, type_1.CloneRest)(references));
}
