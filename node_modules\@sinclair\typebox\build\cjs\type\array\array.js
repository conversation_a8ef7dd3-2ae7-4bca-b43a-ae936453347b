"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Array = Array;
const type_1 = require("../clone/type");
const index_1 = require("../symbols/index");
/** `[<PERSON><PERSON>]` Creates an Array type */
function Array(schema, options = {}) {
    return {
        ...options,
        [index_1.Kind]: 'Array',
        type: 'array',
        items: (0, type_1.CloneType)(schema),
    };
}
